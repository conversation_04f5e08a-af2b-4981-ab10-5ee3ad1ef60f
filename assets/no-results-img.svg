<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 52.3 58.4">
  <!-- Generator: Adobe Illustrator 29.5.0, SVG Export Plug-In . SVG Version: 2.1.0 Build 137)  -->
  <defs>
    <style>
      .st0 {
        fill: none;
      }

      .st1 {
        fill: #ff391f;
      }

      .st2 {
        fill-rule: evenodd;
      }

      .st2, .st3 {
        fill: #fff;
      }

      .st4 {
        fill: #e1ecee;
      }

      .st5 {
        fill: #bdcedb;
      }

      .st6 {
        fill: #bfd6dc;
      }

      .st7 {
        fill: #ffe9d6;
      }

      .st8 {
        fill: url(#linear-gradient);
        fill-opacity: .3;
      }
    </style>
    <linearGradient id="linear-gradient" x1="26.1" y1="7.8" x2="26.1" y2="60.1" gradientTransform="translate(0 66.2) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#bdcedb" stop-opacity="0"/>
      <stop offset="1" stop-color="#bdcedb"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2">
    <g>
      <g>
        <path class="st8" d="M50.3,42.3c-.2.6-.5,1.2-.8,1.8-1.1-1-2.5-1.6-4-1.9-.2,0-.3,0-.5,0-.2,0-.3,0-.5,0-.2,0-.3,0-.5,0-3.1,0-5.8,1.7-7.2,4.2,0,.2-.2.3-.2.5,0,.2-.2.3-.2.5-.4.9-.6,2-.6,3.1,0,2.1.8,4,2,5.4-3.7,1.8-7.7,2.8-11.8,2.8-6.9,0-13.6-2.8-18.5-7.7C2.8,45.8,0,39.2,0,32.3s1.5-10.2,4.4-14.5c2.9-4.3,7-7.6,11.7-9.6,4.8-2,10-2.5,15.1-1.5,5.1,1,9.7,3.5,13.4,7.2,3.7,3.6,6.1,8.3,7.2,13.4,1,5.1.5,10.3-1.5,15.1Z"/>
        <g>
          <g>
            <g>
              <path class="st3" d="M45,21v21.1c-.2,0-.3,0-.5,0-.2,0-.3,0-.5,0-3.1,0-5.8,1.7-7.2,4.2,0,.2-.2.3-.2.5H9.6c-1.2,0-2.1-.9-2.1-2.1v-23.6c0-1.1.9-2.1,2.1-2.1h33.2c1.2,0,2.3.9,2.3,2.1Z"/>
              <path class="st5" d="M42.8,18.5H9.6c-1.4,0-2.6,1.1-2.6,2.5v23.6c0,1.4,1.1,2.5,2.6,2.5h27c0-.2.1-.3.2-.5,0-.2.1-.3.2-.5H9.6c-.9,0-1.7-.7-1.7-1.6v-23.5c0-.9.7-1.6,1.7-1.6h33.2c1,0,1.8.8,1.8,1.6v21c.2,0,.3,0,.5,0,.2,0,.3,0,.5,0v-21.1c0-1.4-1.3-2.5-2.7-2.5Z"/>
            </g>
            <g>
              <path class="st0" d="M26.4,46.7v-27.7"/>
              <rect class="st5" x="12.6" y="32.4" width="27.7" height=".9" transform="translate(-6.5 59.2) rotate(-89.7)"/>
            </g>
            <rect class="st5" x="7.4" y="24.3" width="37.6" height=".9"/>
          </g>
          <path class="st6" d="M10.8,22.8c.5,0,.9-.4.9-.9s-.4-.9-.9-.9-.9.4-.9.9.4.9.9.9Z"/>
          <path class="st6" d="M13.5,22.8c.5,0,.9-.4.9-.9s-.4-.9-.9-.9-.9.4-.9.9.4.9.9.9Z"/>
          <path class="st6" d="M16.3,22.8c.5,0,.9-.4.9-.9s-.4-.9-.9-.9-.9.4-.9.9.4.9.9.9Z"/>
          <path class="st6" d="M16,28h-5.3c-.3,0-.6-.3-.6-.5s.3-.5.6-.5h5.3c.3,0,.6.3.6.5s-.3.5-.6.5Z"/>
          <path class="st6" d="M14.7,36.4h-3.9c-.3,0-.6-.3-.6-.5s.3-.5.6-.5h3.9c.3,0,.6.3.6.5,0,.2-.3.5-.6.5Z"/>
          <path class="st4" d="M19.5,39.2h-6.7c-.3,0-.6-.3-.6-.5s.3-.5.6-.5h6.7c.3,0,.6.3.6.5,0,.3-.3.5-.6.5Z"/>
          <path class="st4" d="M26.1,38.2v1h-4c-.3,0-.6-.3-.6-.5s0-.3.2-.4c.1,0,.2-.2.4-.2h4Z"/>
          <path class="st6" d="M18.5,41.9h-7.7c-.3,0-.6-.3-.6-.5s.3-.5.6-.5h7.7c.3,0,.6.3.6.5s-.2.5-.6.5Z"/>
          <path class="st4" d="M14.2,44.8h-3.3c-.3,0-.6-.3-.6-.5s.3-.5.6-.5h3.3c.3,0,.6.3.6.5s-.2.5-.6.5Z"/>
          <path class="st4" d="M17,44.8h-.7c-.3,0-.6-.3-.6-.5s.3-.5.6-.5h.7c.3,0,.6.3.6.5s-.3.5-.6.5Z"/>
          <path class="st4" d="M12.3,33.7h-1.6c-.3,0-.6-.3-.6-.5s.3-.5.6-.5h1.6c.3,0,.6.3.6.5s-.3.5-.6.5Z"/>
          <path class="st4" d="M18.5,30.8h-7.7c-.3,0-.6-.3-.6-.5s.3-.5.6-.5h7.7c.3,0,.6.3.6.5s-.2.5-.6.5Z"/>
          <path class="st6" d="M26.1,29.8v1h-5.1c-.3,0-.6-.3-.6-.5s0-.3.2-.4c0-.1.2-.2.4-.2h5.1,0Z"/>
        </g>
      </g>
      <g>
        <path class="st2" d="M38.9,41.3l-4.4-4.4c.5-.8.9-1.8.9-2.9,0-2.9-2.3-5.2-5.2-5.2s-2.3.4-3.2,1.1c-.2.1-.3.3-.5.4s-.2.3-.4.4c0,0,0,0,0,0,0,0,0,0,0,0-.7.9-1.1,2-1.1,3.2s.4,2.2,1,3.1c.1.2.3.4.5.5.1.1.3.3.5.4.9.7,2,1.1,3.2,1.1s2-.3,2.8-.9l4.4,4.4c.4.4,1.1.4,1.5,0,.4-.4.4-1.1,0-1.5ZM30.1,37.1c-1.7,0-3.1-1.4-3.1-3.1s1.4-3.1,3.1-3.1,3.1,1.4,3.1,3.1-1.4,3.1-3.1,3.1Z"/>
        <path class="st6" d="M30.1,30.6c-1.4,0-2.7.9-3.2,2.2-.2.4-.2.8-.2,1.3s0,.8.2,1.2c.5,1.3,1.7,2.2,3.2,2.2s3.4-1.5,3.4-3.4-1.5-3.4-3.4-3.4ZM30.1,36.8c-1.5,0-2.8-1.2-2.8-2.8s1.2-2.8,2.8-2.8,2.8,1.2,2.8,2.8-1.2,2.8-2.8,2.8ZM30.1,30.6c-1.4,0-2.7.9-3.2,2.2-.2.4-.2.8-.2,1.3s0,.8.2,1.2c.5,1.3,1.7,2.2,3.2,2.2s3.4-1.5,3.4-3.4-1.5-3.4-3.4-3.4ZM30.1,36.8c-1.5,0-2.8-1.2-2.8-2.8s1.2-2.8,2.8-2.8,2.8,1.2,2.8,2.8-1.2,2.8-2.8,2.8ZM39.1,41l-4.2-4.2c.5-.9.8-1.8.8-2.8,0-3-2.5-5.5-5.5-5.5s-2.3.4-3.2,1c-.2.1-.3.2-.5.4-.1.1-.2.2-.4.4,0,0,0,0,0,0-.1.1-.3.3-.4.5-.6.9-1,2-1,3.2s.5,2.7,1.4,3.6c.1.2.3.3.5.5.1.1.3.2.5.4.9.7,2,1,3.2,1s2-.3,2.8-.8l4.2,4.2c.3.3.6.4,1,.4s.7-.1,1-.4c.2-.3.4-.6.4-1s-.1-.7-.4-1ZM38.6,42.5c-.3.3-.7.3-1,0l-4.4-4.4c0,0-.2,0-.2,0s-.1,0-.2,0c-.8.5-1.7.8-2.7.8s-2.4-.5-3.2-1.2c-.2-.1-.3-.3-.5-.4-.2-.2-.3-.4-.5-.6-.5-.7-.7-1.6-.7-2.5s.3-1.9.8-2.6c.1-.2.3-.4.5-.6.1-.2.3-.3.5-.5.9-.8,2-1.2,3.2-1.2,2.7,0,4.8,2.2,4.8,4.9s-.3,1.9-.8,2.7c0,.1,0,.3,0,.4l4.4,4.4c.3.3.3.7,0,1ZM30.1,30.6c-1.4,0-2.7.9-3.2,2.2-.2.4-.2.8-.2,1.3s0,.8.2,1.2c.5,1.3,1.7,2.2,3.2,2.2s3.4-1.5,3.4-3.4-1.5-3.4-3.4-3.4ZM30.1,36.8c-1.5,0-2.8-1.2-2.8-2.8s1.2-2.8,2.8-2.8,2.8,1.2,2.8,2.8-1.2,2.8-2.8,2.8Z"/>
      </g>
      <g>
        <path class="st3" d="M50.4,50.3c0,3.5-2.8,6.3-6.3,6.3-1.8,0-3.3-.7-4.5-1.9-1.1-1.1-1.8-2.7-1.8-4.5,0-1.1.3-2.2.8-3.1,0-.2.2-.3.3-.5,0-.2.2-.3.3-.5,1.2-1.4,2.9-2.3,4.9-2.3.2,0,.3,0,.5,0,.2,0,.3,0,.5,0,.2,0,.3,0,.5,0,1.2.3,2.2.8,3,1.7,1.2,1.2,1.9,2.7,1.9,4.5Z"/>
        <path class="st1" d="M50.4,50.3c0,3.5-2.8,6.3-6.3,6.3-1.8,0-3.3-.7-4.5-1.9-1.1-1.1-1.8-2.7-1.8-4.5,0-1.1.3-2.2.8-3.1,0-.2.2-.3.3-.5,0-.2.2-.3.3-.5,1.2-1.4,2.9-2.3,4.9-2.3.2,0,.3,0,.5,0,.2,0,.3,0,.5,0,.2,0,.3,0,.5,0,1.2.3,2.2.8,3,1.7,1.2,1.2,1.9,2.7,1.9,4.5Z"/>
        <g>
          <path class="st3" d="M45.3,48.6l-1.3,1.3-1.3-1.3c0,0-.1,0-.2,0s-.2,0-.2,0,0,.1,0,.2,0,.2,0,.2l1.3,1.3-1.3,1.3c0,0,0,.1,0,.2s0,.2,0,.2.1,0,.2,0,.2,0,.2,0l1.3-1.3,1.3,1.3c0,0,.1,0,.2,0s.2,0,.2,0,0-.1,0-.2,0-.2,0-.2l-1.3-1.3,1.3-1.3c0,0,0-.1,0-.2s0-.2,0-.2-.1,0-.2,0-.2,0-.2,0Z"/>
          <path class="st3" d="M44.9,50.5l-.2-.2,1.2-1.2c0,0,.1-.2.1-.3s0-.2-.1-.3c-.2-.2-.5-.2-.7,0l-1.2,1.2-1.2-1.2c-.2-.2-.5-.2-.7,0,0,0-.1.2-.1.3s0,.2.1.3l1.2,1.2-1.2,1.2c0,0-.1.2-.1.3s0,.2.1.3c0,0,.2.1.3.1s.2,0,.3-.1l1.2-1.2.2.2,1,1c0,0,.2.1.3.1s.2,0,.3-.1.1-.2.1-.3,0-.2-.1-.3c0,0-1-1-1-1ZM45.7,52s0,0-.1,0,0,0-.1,0l-1-1-.4-.4-1.4,1.4s0,0-.1,0,0,0-.1,0,0,0,0-.1,0,0,0-.1l1.4-1.4-1.4-1.4s0,0,0-.1,0,0,0-.1c0,0,.2,0,.3,0l1.4,1.4,1.4-1.4c0,0,.2,0,.3,0,0,0,0,0,0,.1s0,0,0,.1l-1.4,1.4.4.4,1,1s0,0,0,.1,0,0,0,.1Z"/>
        </g>
      </g>
    </g>
  </g>
  <g>
    <path class="st1" d="M43.5,0H8.8c-.5,0-1,.2-1.4.6-.4.4-.6.9-.6,1.4v12.1c0,.5.2,1,.6,1.4.4.4.9.6,1.4.6h34.7c.5,0,1-.2,1.4-.6.4-.4.6-.9.6-1.4V2c0-.5-.2-1-.6-1.4-.4-.4-.9-.6-1.4-.6Z"/>
    <path class="st7" d="M34.4,4.4h-14.1c0,0-.2,0-.3,0,0,0-.2,0-.2.1,0,0-.1.1-.1.2,0,0,0,.2,0,.3s0,.2,0,.3c0,0,0,.2.1.2,0,0,.1.1.2.1,0,0,.2,0,.3,0h14.1c.2,0,.3,0,.5-.2.1-.1.2-.3.2-.5s0-.3-.2-.5c-.1-.1-.3-.2-.5-.2Z"/>
    <path class="st3" d="M34.4,10.8h-14.1c-.2,0-.3,0-.5.2-.1.1-.2.3-.2.5s0,.3.2.5c.1.1.3.2.5.2h14.1c0,0,.2,0,.3,0,0,0,.2,0,.2-.1,0,0,.1-.1.1-.2,0,0,0-.2,0-.3s0-.2,0-.3c0,0,0-.2-.1-.2,0,0-.1-.1-.2-.1,0,0-.2,0-.3,0Z"/>
    <path class="st3" d="M41.3,6.6h-21c-.2,0-.3,0-.5.2-.1.1-.2.3-.2.5s0,.3.2.5c.1.1.3.2.5.2h21c.2,0,.3,0,.5-.2.1-.1.2-.3.2-.5s0-.3-.2-.5c-.1-.1-.3-.2-.5-.2Z"/>
    <path class="st3" d="M39,8.7h-18.7c-.2,0-.3,0-.5.2-.1.1-.2.3-.2.5s0,.3.2.5c.1.1.3.2.5.2h18.7c.2,0,.3,0,.5-.2.1-.1.2-.3.2-.5s0-.3-.2-.5c-.1-.1-.3-.2-.5-.2Z"/>
    <path class="st3" d="M17.3,5.8c-.2-.2-.4-.3-.6-.4,0-.2,0-.5-.3-.7-.2-.2-.4-.3-.6-.3,0,0,0,0,0,0h-3.5s0,0,0,0c-.2,0-.3,0-.4.2-.1,0-.2.2-.3.4,0,.1-.1.3-.1.5-.3,0-.5.3-.7.5-.2.2-.2.5-.2.8l.4,4.2c0,.3.2.6.4.8.2.2.5.3.8.3h4c.3,0,.6-.1.8-.3.2-.2.4-.5.4-.8l.4-4.2c0-.2,0-.3,0-.5,0-.2-.1-.3-.3-.4M15.5,7.8c0,.4-.2.8-.4,1.1-.3.3-.7.4-1.1.4s-.8-.2-1.1-.4-.4-.7-.4-1.1v-.2c0,0,0-.2.1-.2,0,0,.2-.1.2-.1s.2,0,.2.1c0,0,.1.2.1.2v.2c0,.2,0,.4.2.6.2.2.4.2.6.2s.4,0,.6-.2c.2-.2.2-.4.2-.6v-.2c0,0,0-.2.1-.2,0,0,.2-.1.2-.1s.2,0,.2.1c0,0,.1.2.1.2v.2ZM12,5.4s0,0,0,0c0,0,0,0,0,0,0,0,0,0,.1,0h3.5s0,0,.1,0c0,0,0,0,0,.1,0,0,0,0,0,0h-4Z"/>
  </g>
</svg>