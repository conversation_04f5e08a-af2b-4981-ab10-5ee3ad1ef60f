// Compression Processor Module

// Compression level constants
const COMPRESSION_LEVELS = {
  ADAPTIVE: 'adaptive',
  LIGHT: 1024,
  BALANCED: 512,
  AGGRESSIVE: 128
};

// Main compression processor class
class CompressionProcessor {
  constructor() {
    // Initialize any required properties
    this.isProcessing = false;
    this.shouldStop = false;
  }

  /**
   * Process an image with the specified compression level
   * @param {File} file - The image file to process
   * @param {string} compressionLevel - The compression level to apply
   * @param {Function} progressCallback - Callback for progress updates
   * @returns {Promise<Blob>} - The compressed image as a Blob
   */
  async processImage(file, compressionLevel, progressCallback) {
    try {
      // Update progress
      progressCallback?.({ stage: 'loading', progress: 0 });

      // Load the image
      const imageData = await this.loadImage(file);
      progressCallback?.({ stage: 'loading', progress: 100 });

      // Apply compression based on level
      progressCallback?.({ stage: 'compression', progress: 0 });
      let compressedBlob;

      switch (compressionLevel) {
        case COMPRESSION_LEVELS.ADAPTIVE:
          compressedBlob = await this.applyAdaptiveCompression(imageData);
          break;
        case COMPRESSION_LEVELS.LIGHT:
          compressedBlob = await this.applyFixedCompression(imageData, COMPRESSION_LEVELS.LIGHT);
          break;
        case COMPRESSION_LEVELS.BALANCED:
          compressedBlob = await this.applyFixedCompression(imageData, COMPRESSION_LEVELS.BALANCED);
          break;
        case COMPRESSION_LEVELS.AGGRESSIVE:
          compressedBlob = await this.applyFixedCompression(imageData, COMPRESSION_LEVELS.AGGRESSIVE);
          break;
        default:
          throw new Error('Invalid compression level');
      }

      progressCallback?.({ stage: 'compression', progress: 100 });
      progressCallback?.({ stage: 'complete', progress: 100 });

      return compressedBlob;
    } catch (error) {
      console.error('Error processing image:', error);
      throw error;
    }
  }

  /**
   * Load an image file and return its data
   * @param {File} file - The image file to load
   * @returns {Promise<Blob>} - The image as a Blob
   */
  async loadImage(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        canvas.toBlob(resolve, 'image/png', 1.0);
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Apply adaptive compression based on file size
   * @param {Blob} blob - The image blob to compress
   * @returns {Promise<Blob>} - The compressed image as a Blob
   */
  async applyAdaptiveCompression(blob) {
    try {
      console.log(`Starting adaptive compression: Original size=${blob.size} bytes`);
      
      // Skip if blob size <= 10MB
      if (blob.size <= 10485760) {
        console.log(`Skipping compression: Size=${blob.size} bytes`);
        return blob;
      }
      
      // Start with highest compression level
      let compressionLevel = 1024;
      let compressedBlob = null;
      let attempts = 0;
      const MAX_ATTEMPTS = 5;
      
      while (attempts < MAX_ATTEMPTS) {
        attempts++;
        
        // Convert blob to arrayBuffer for UPNG processing
        const arrayBuffer = await blob.arrayBuffer();
        
        // Decode PNG using UPNG
        const pngData = UPNG.decode(arrayBuffer);
        
        // Get RGBA8 data
        const rgba8Data = UPNG.toRGBA8(pngData)[0];
        
        // Re-encode with compression
        const compressedData = UPNG.encode([rgba8Data], pngData.width, pngData.height, compressionLevel);
        
        // Create new blob from compressed data
        compressedBlob = new Blob([compressedData], { type: 'image/png' });
        
        console.log(`Compression attempt ${attempts} with level ${compressionLevel}, size: ${compressedBlob.size} bytes`);
        
        // Check if size is acceptable or we've reached minimum compression
        if (compressedBlob.size <= 20971520 || compressionLevel <= 128) {
          break;
        }
        
        // Reduce compression level for next attempt
        compressionLevel = Math.floor(compressionLevel / 2);
      }
      
      console.log(`Final compressed size: ${compressedBlob.size} bytes (${Math.round(compressedBlob.size / blob.size * 100)}% of original)`);
      return compressedBlob;
    } catch (error) {
      console.error('Adaptive compression failed:', error);
      return blob; // Return original if compression fails
    }
  }

  /**
   * Apply fixed level compression
   * @param {Blob} blob - The image blob to compress
   * @param {number} level - The compression level to apply
   * @returns {Promise<Blob>} - The compressed image as a Blob
   */
  async applyFixedCompression(blob, level) {
    try {
      console.log(`Starting fixed compression (level ${level}): Original size=${blob.size} bytes`);
      
      // Convert blob to arrayBuffer for UPNG processing
      const arrayBuffer = await blob.arrayBuffer();
      
      // Decode PNG using UPNG
      const pngData = UPNG.decode(arrayBuffer);
      
      // Get RGBA8 data
      const rgba8Data = UPNG.toRGBA8(pngData)[0];
      
      // Re-encode with compression
      const compressedData = UPNG.encode([rgba8Data], pngData.width, pngData.height, level);
      
      // Create new blob from compressed data
      const compressedBlob = new Blob([compressedData], { type: 'image/png' });
      
      console.log(`Final compressed size: ${compressedBlob.size} bytes (${Math.round(compressedBlob.size / blob.size * 100)}% of original)`);
      return compressedBlob;
    } catch (error) {
      console.error(`Fixed compression (level ${level}) failed:`, error);
      return blob; // Return original if compression fails
    }
  }

  /**
   * Stop the current processing operation
   */
  stop() {
    this.shouldStop = true;
  }

  /**
   * Reset the processor state
   */
  reset() {
    this.isProcessing = false;
    this.shouldStop = false;
  }
}

// Export the processor instance
window.compressionProcessor = new CompressionProcessor(); 