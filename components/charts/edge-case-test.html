<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snap Charts - Edge Case Tests</title>
    <link rel="stylesheet" href="snap-charts.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .chart-container {
            height: 300px;
            margin: 20px 0;
        }
        .test-description {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
            line-height: 1.5;
        }
        .expected-result {
            background: #e8f5e8;
            border-left: 4px solid #04AE2C;
            padding: 10px 15px;
            margin-top: 10px;
            font-size: 13px;
        }
        .test-case {
            background: #fff3e0;
            border-left: 4px solid #f57c00;
            padding: 10px 15px;
            margin-bottom: 10px;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Snap Charts - Edge Case Tests</h1>
        <p>This page tests specific edge cases for percentage calculations in comparison mode.</p>
        
        <div class="test-section">
            <div class="test-title">Test 1: Both Periods Have Zero Returns</div>
            <div class="test-description">
                <div class="test-case">
                    <strong>Test Case:</strong> Current = 0 returns, Previous = 0 returns
                </div>
                <div class="expected-result">
                    <strong>Expected:</strong> No percentage indicator shown (just the value)
                </div>
            </div>
            <div id="test1Chart" class="chart-container"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Test 2: Previous Zero, Current Has Returns</div>
            <div class="test-description">
                <div class="test-case">
                    <strong>Test Case:</strong> Current = 5 returns, Previous = 0 returns
                </div>
                <div class="expected-result">
                    <strong>Expected:</strong> +100% with ↑ (red color, indicating new returns - bad)
                </div>
            </div>
            <div id="test2Chart" class="chart-container"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Test 3: Current Zero, Previous Has Returns</div>
            <div class="test-description">
                <div class="test-case">
                    <strong>Test Case:</strong> Current = 0 returns, Previous = 8 returns
                </div>
                <div class="expected-result">
                    <strong>Expected:</strong> -100% with ↓ (green color, indicating eliminated returns - good)
                </div>
            </div>
            <div id="test3Chart" class="chart-container"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Test 4: Both Periods Have Zero Sales</div>
            <div class="test-description">
                <div class="test-case">
                    <strong>Test Case:</strong> Current = 0 sales, Previous = 0 sales
                </div>
                <div class="expected-result">
                    <strong>Expected:</strong> No percentage indicator shown (just the value)
                </div>
            </div>
            <div id="test4Chart" class="chart-container"></div>
        </div>
    </div>

    <script src="dummy-data.js"></script>
    <script src="data-loader.js"></script>
    <script src="snap-charts.js"></script>
    <script>
        // Test 1: Both periods have zero returns
        const test1Data = {
            current: [{
                month: 'JAN', day: '01', year: '25',
                marketplaces: [
                    { code: "US", sales: 100, royalties: 35, returns: 0 },
                    { code: "UK", sales: 80, royalties: 28, returns: 0 }
                ],
                sales: 180, royalties: 63, returns: 0,
                values: [100, 80], labels: ["US", "UK"]
            }],
            comparison: [{
                month: 'JAN', day: '01', year: '24',
                marketplaces: [
                    { code: "US", sales: 90, royalties: 32, returns: 0 },
                    { code: "UK", sales: 70, royalties: 25, returns: 0 }
                ],
                sales: 160, royalties: 57, returns: 0,
                values: [90, 70], labels: ["US", "UK"]
            }]
        };

        // Test 2: Previous zero, current has returns
        const test2Data = {
            current: [{
                month: 'JAN', day: '02', year: '25',
                marketplaces: [
                    { code: "US", sales: 100, royalties: 35, returns: 3 },
                    { code: "UK", sales: 80, royalties: 28, returns: 2 }
                ],
                sales: 180, royalties: 63, returns: 5,
                values: [100, 80], labels: ["US", "UK"]
            }],
            comparison: [{
                month: 'JAN', day: '02', year: '24',
                marketplaces: [
                    { code: "US", sales: 90, royalties: 32, returns: 0 },
                    { code: "UK", sales: 70, royalties: 25, returns: 0 }
                ],
                sales: 160, royalties: 57, returns: 0,
                values: [90, 70], labels: ["US", "UK"]
            }]
        };

        // Test 3: Current zero, previous has returns
        const test3Data = {
            current: [{
                month: 'JAN', day: '03', year: '25',
                marketplaces: [
                    { code: "US", sales: 100, royalties: 35, returns: 0 },
                    { code: "UK", sales: 80, royalties: 28, returns: 0 }
                ],
                sales: 180, royalties: 63, returns: 0,
                values: [100, 80], labels: ["US", "UK"]
            }],
            comparison: [{
                month: 'JAN', day: '03', year: '24',
                marketplaces: [
                    { code: "US", sales: 90, royalties: 32, returns: 5 },
                    { code: "UK", sales: 70, royalties: 25, returns: 3 }
                ],
                sales: 160, royalties: 57, returns: 8,
                values: [90, 70], labels: ["US", "UK"]
            }]
        };

        // Test 4: Both periods have zero sales
        const test4Data = {
            current: [{
                month: 'JAN', day: '04', year: '25',
                marketplaces: [
                    { code: "US", sales: 0, royalties: 0, returns: 0 },
                    { code: "UK", sales: 0, royalties: 0, returns: 0 }
                ],
                sales: 0, royalties: 0, returns: 0,
                values: [0, 0], labels: ["US", "UK"]
            }],
            comparison: [{
                month: 'JAN', day: '04', year: '24',
                marketplaces: [
                    { code: "US", sales: 0, royalties: 0, returns: 0 },
                    { code: "UK", sales: 0, royalties: 0, returns: 0 }
                ],
                sales: 0, royalties: 0, returns: 0,
                values: [0, 0], labels: ["US", "UK"]
            }]
        };

        // Initialize all test charts
        function initializeTests() {
            // Test 1
            new SnapChart({
                container: '#test1Chart',
                type: 'stacked-column',
                data: test1Data.current,
                options: {
                    compareMode: true,
                    compareData: test1Data.comparison,
                    animate: false,
                    responsive: true
                }
            });

            // Test 2
            new SnapChart({
                container: '#test2Chart',
                type: 'stacked-column',
                data: test2Data.current,
                options: {
                    compareMode: true,
                    compareData: test2Data.comparison,
                    animate: false,
                    responsive: true
                }
            });

            // Test 3
            new SnapChart({
                container: '#test3Chart',
                type: 'stacked-column',
                data: test3Data.current,
                options: {
                    compareMode: true,
                    compareData: test3Data.comparison,
                    animate: false,
                    responsive: true
                }
            });

            // Test 4
            new SnapChart({
                container: '#test4Chart',
                type: 'stacked-column',
                data: test4Data.current,
                options: {
                    compareMode: true,
                    compareData: test4Data.comparison,
                    animate: false,
                    responsive: true
                }
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initializeTests);
    </script>
</body>
</html>
