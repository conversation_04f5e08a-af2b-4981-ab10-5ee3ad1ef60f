# Snap Dashboard - J<PERSON> Bundle

This project has been refactored to work as a single JavaScript bundle, making it compatible with Chrome Extension development.

## Project Structure

### Core Files
- `index.html` - Minimal HTML shell that loads global CSS and the main JS application
- `snapapp.css` - Global styles (unchanged from original)
- `snapapp.js` - Main application JavaScript (now includes the HTML structure previously in snapapp.html)

### Components
Each component is now a single JS file that contains:
1. HTML template as a string
2. Component-specific CSS as a string
3. JavaScript logic for the component

Example component structure:
```js
// Component-specific CSS as a string
const componentCSS = `/* CSS rules here */`;

// Component HTML as a string
const componentHTML = `<div>HTML structure here</div>`;

// Inject component CSS if not already injected
if (!document.getElementById('component-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'component-styles';
  styleElement.textContent = componentCSS;
  document.head.appendChild(styleElement);
}

// Component logic
function initComponent() {
  // Set HTML content
  const mainContent = document.querySelector('.main-content');
  if (mainContent) {
    mainContent.innerHTML = componentHTML;
  }
  
  // Component-specific JavaScript
  // ...
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initComponent);
} else {
  initComponent();
}

// Export component data
window.componentName = {
  render: initComponent,
  html: componentHTML,
  css: componentCSS
};
```

## How It Works

1. When the page loads, `index.html` loads `snapapp.css` and `snapapp.js`
2. `snapapp.js` creates the UI shell (sidebar, etc.) and initializes the main app
3. When a sidebar button is clicked, the corresponding component JS file is loaded
4. The component JS file:
   - Injects its CSS if not already present
   - Adds its HTML to the main content area
   - Initializes event listeners and component-specific functionality

## Adding a New Component

1. Create a new folder in `components/` with your component name
2. Create a single JS file named after your component (e.g., `mycomponent.js`)
3. Follow the structure of existing components (dashboard.js or snap-image-studio.js)
4. Add your component to the `componentMap` in `snapapp.js` if it needs to be accessible from the sidebar

## Chrome Extension Integration

To convert this to a Chrome extension:
1. Create a `manifest.json` file in the root directory
2. Update asset paths to use `chrome.runtime.getURL()` where needed
3. Specify appropriate permissions in the manifest
4. Package the extension according to Chrome's requirements

## Development

During development, you can test the application by opening `index.html` in a browser.

To deploy as a Chrome extension, the structure ensures all HTML/CSS is embedded in JS files, making the extension simpler to package and maintain. 