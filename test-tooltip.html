<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Today vs Previous Years Chart - Tooltip Test</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Today vs Previous Years Chart - Tooltip Test</h1>
        
        <div class="info">
            <h3>Test Objectives:</h3>
            <ul>
                <li>✅ Generate 26 years of data (2000-2025)</li>
                <li>✅ Use today's calendar date for all years</li>
                <li>✅ Format dates as "MMM DD, YYYY"</li>
                <li>🔍 Test tooltip functionality in production mode</li>
                <li>🔍 Verify horizontal scrolling works</li>
                <li>🔍 Check marketplace filtering</li>
            </ul>
        </div>

        <div id="status" class="status warning">Initializing chart...</div>

        <div id="chart-container" class="chart-container"></div>

        <div class="info">
            <h3>Instructions:</h3>
            <ol>
                <li>Hover over any column to test tooltip functionality</li>
                <li>Scroll horizontally to see all 26 years (2000-2025)</li>
                <li>Check that current year (2025) is visible on the right</li>
                <li>Verify tooltip shows: Date, Sales, Royalties, Returns</li>
                <li>Test marketplace dropdown if available</li>
            </ol>
        </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Generate test data using the same function as dashboard
        function generateTestData() {
            const data = [];
            const startYear = 2000;
            const endYear = 2025;
            
            // Get today's month and day for consistent date across all years
            const today = new Date();
            const todayMonth = today.getMonth(); // 0-based month
            const todayDay = today.getDate();
            
            // Marketplace codes that should be included
            const marketplaceCodes = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];

            for (let year = startYear; year <= endYear; year++) {
                const marketplaces = [];
                let totalSales = 0;
                let totalRoyalties = 0;
                let totalReturns = 0;

                // Create the date for this year using today's month/day
                const yearDate = new Date(year, todayMonth, todayDay);
                const formattedDate = yearDate.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric', 
                    year: 'numeric'
                });

                // Generate marketplace data for this year with realistic growth trends
                marketplaceCodes.forEach(code => {
                    // Implement realistic business growth over 26 years
                    let baseSalesMin, baseSalesMax;
                    
                    if (year <= 2005) {
                        // Early years: 2000-2005
                        baseSalesMin = 15;
                        baseSalesMax = 50;
                    } else if (year <= 2010) {
                        // Growth period: 2006-2010  
                        baseSalesMin = 25;
                        baseSalesMax = 70;
                    } else if (year <= 2015) {
                        // Established business: 2011-2015
                        baseSalesMin = 35;
                        baseSalesMax = 80;
                    } else if (year <= 2020) {
                        // Mature business: 2016-2020
                        baseSalesMin = 45;
                        baseSalesMax = 90;
                    } else {
                        // Current levels: 2021-2025
                        baseSalesMin = 25;
                        baseSalesMax = 90;
                    }
                    
                    // Apply marketplace-specific multipliers
                    const marketplaceMultipliers = {
                        'US': 1.0,
                        'UK': 0.7,
                        'DE': 0.6,
                        'FR': 0.5,
                        'IT': 0.4,
                        'ES': 0.35,
                        'JP': 0.3
                    };
                    
                    // Generate base sales within the range for this period
                    const baseSales = Math.floor(Math.random() * (baseSalesMax - baseSalesMin + 1)) + baseSalesMin;
                    const adjustedBaseSales = Math.floor(baseSales * marketplaceMultipliers[code]);
                    
                    // Add occasional spike days (10-15% chance of 150-200 units)
                    const isSpike = Math.random() < 0.125; // 12.5% chance
                    const sales = isSpike ? 
                        Math.floor(Math.random() * 51) + 150 : // 150-200 spike range
                        Math.max(adjustedBaseSales, 10); // Minimum 10 sales

                    const royalties = Math.floor(sales * 0.15); // 15% royalty rate
                    const returns = Math.floor(sales * 0.05); // 5% return rate

                    marketplaces.push({
                        code: code,
                        sales: sales,
                        royalties: royalties,
                        returns: returns
                    });

                    totalSales += sales;
                    totalRoyalties += royalties;
                    totalReturns += returns;
                });

                // Create data point in the format expected by scrollable-stacked-column
                const dataPoint = {
                    month: formattedDate, // Use formatted date (e.g., "Jun 26, 2025")
                    year: year.toString().slice(-2), // 2-digit year for compatibility
                    marketplaces: marketplaces,
                    sales: totalSales,
                    royalties: totalRoyalties,
                    returns: totalReturns,
                    values: marketplaces.map(mp => mp.sales),
                    labels: marketplaces.map(mp => mp.code)
                };

                data.push(dataPoint);
            }

            return data;
        }

        // Initialize the chart
        function initChart() {
            const statusEl = document.getElementById('status');
            
            try {
                statusEl.textContent = 'Generating 26 years of data...';
                statusEl.className = 'status warning';
                
                const chartData = generateTestData();
                console.log('Generated data for years:', chartData.map(d => d.month));
                
                statusEl.textContent = 'Creating chart...';
                
                const chart = new SnapChart({
                    container: '#chart-container',
                    type: 'scrollable-stacked-column',
                    data: chartData,
                    
                    // Production mode - just the chart, no demo components
                    demoOptions: {
                        showContainer: false,    // No border container
                        showTitle: false,        // No title section
                        showDataEditor: false,   // No data editor
                        showControls: false,     // No header controls
                        showInsights: false      // No insights panel
                    },
                    
                    options: {
                        responsive: true,
                        animate: true,
                        height: 350
                    }
                });
                
                statusEl.textContent = `✅ Chart initialized successfully! Showing ${chartData.length} years of data. Hover over columns to test tooltips.`;
                statusEl.className = 'status success';
                
            } catch (error) {
                console.error('Chart initialization failed:', error);
                statusEl.textContent = `❌ Chart initialization failed: ${error.message}`;
                statusEl.className = 'status error';
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initChart);
    </script>
</body>
</html>
